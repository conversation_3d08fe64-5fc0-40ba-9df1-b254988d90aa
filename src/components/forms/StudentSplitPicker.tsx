import React from 'react';
import { User, UserRole } from '../../types/User';
import { apiService } from '../../services/apiService';
import { mockUsers } from '../../store/mockData/users';
import UserPicker from './UserPicker';

interface StudentSplitPickerProps {
  control: any;
  name: string;
  error?: string;
}

export default function StudentSplitPicker({
  control,
  name,
  error,
}: StudentSplitPickerProps) {

  // Fetch students for split selection
  const fetchStudents = async (): Promise<User[]> => {
    try {
      const fetchedStudents = await apiService.getStudents();
      return fetchedStudents;
    } catch (error) {
      console.error('Failed to fetch students:', error);
      // Fallback to mock data if API fails
      const fallbackStudents = mockUsers.filter(user => user.role === UserRole.STUDENT);
      return fallbackStudents;
    }
  };

  // Filter to only show students
  const studentFilter = (user: User): boolean => {
    return user.role === UserRole.STUDENT;
  };

  return (
    <UserPicker
      control={control}
      name={name}
      label="Split with Other users (Optional - Equal split only)"
      placeholder="Select students to split with"
      error={error}
      multiple={true}
      userFilter={studentFilter}
      fetchUsers={fetchStudents}
      required={false}
    />
  );
}
