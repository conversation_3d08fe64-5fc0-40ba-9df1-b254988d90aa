import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
import { Platform } from 'react-native';
import { config, debugLog } from '../config/env';
import { apiService } from './apiService';
import { storageService, StoredUserData } from './storageService';

// Google OAuth configuration
const GOOGLE_CONFIG = {
  iosClientId: config.googleIosClientId,
};

export interface GoogleUser {
  id: string;
  email: string;
  name: string;
  photo?: string;
  givenName?: string;
  familyName?: string;
}

export interface GoogleAuthResult {
  user: any; // User from our backend
  googleUser: GoogleUser;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

export class GoogleAuthService {
  private static instance: GoogleAuthService;
  private isConfigured = false;

  public static getInstance(): GoogleAuthService {
    if (!GoogleAuthService.instance) {
      GoogleAuthService.instance = new GoogleAuthService();
    }
    return GoogleAuthService.instance;
  }

  private async configure(): Promise<void> {
    if (this.isConfigured) return;

    try {
      await GoogleSignin.configure({
        iosClientId: GOOGLE_CONFIG.iosClientId,
        offlineAccess: false,
        hostedDomain: 'thinkglobalschool.com', // Restrict to TGS domain
        forceCodeForRefreshToken: true,
        accountName: '',
        googleServicePlistPath: '',
        openIdRealm: '',
        profileImageSize: 120,
      });
      this.isConfigured = true;
    } catch (error) {
      console.error('Google Sign-In configuration error:', error);
      // For demo purposes, we'll continue without throwing
    }
  }

  async signIn(): Promise<GoogleAuthResult | null> {
    try {
      await this.configure();
      debugLog('Starting Google Sign-In process');

      let googleUser: GoogleUser;
      let googleTokens: any = {};

      // For demo purposes, we'll simulate Google login with mock data
      // In production, you would use the actual Google Sign-In flow

      if (__DEV__) {
        // Mock Google login for development
        googleUser = await this.mockGoogleLogin();
        googleTokens = {
          accessToken: 'c4ecde5c-702c-4a7f-8739-047c93d9eb6c',
          idToken: 'c4ecde5c-702c-4a7f-8739-047c93d9eb6c',
        };
      } else {
        // Check if device supports Google Play Services (Android)
        await GoogleSignin.hasPlayServices();

        // Attempt to sign in
        const userInfo = await GoogleSignin.signIn();

        // Validate domain
        if (!userInfo.user.email.endsWith('@thinkglobalschool.com')) {
          await GoogleSignin.signOut();
          throw new Error('Please use a Think Global School email address');
        }

        googleUser = {
          id: userInfo.user.id,
          email: userInfo.user.email,
          name: userInfo.user.name || '',
          photo: userInfo.user.photo || undefined,
          givenName: userInfo.user.givenName || undefined,
          familyName: userInfo.user.familyName || undefined,
        };

        googleTokens = userInfo.idToken ? {
          accessToken: userInfo.idToken,
          idToken: userInfo.idToken,
        } : {};
      }

      // Authenticate with our backend
      debugLog('Authenticating with backend API');
      const authResponse = await apiService.authenticateWithGoogle(googleUser, googleTokens);

      // Store tokens and user data
      const tokens = {
        accessToken: authResponse.access_token,
        refreshToken: authResponse.refresh_token,
      };

      await storageService.storeTokens(tokens);

      const storedUserData: StoredUserData = {
        user: authResponse.user,
        googleUser,
        tokens,
        lastLogin: new Date().toISOString(),
      };

      await storageService.storeUserData(storedUserData);
      debugLog('User data stored successfully');

      return {
        user: authResponse.user,
        googleUser,
        tokens,
      };

    } catch (error: any) {
      debugLog('Google Sign-In Error:', error);

      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        return null; // User cancelled
      } else if (error.code === statusCodes.IN_PROGRESS) {
        throw new Error('Sign in is already in progress');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        throw new Error('Google Play Services not available');
      } else {
        throw error;
      }
    }
  }

  private async mockGoogleLogin(): Promise<GoogleUser> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Mock Google user data - in production this would come from Google
    const mockGoogleUsers = [
      {
        id: 'google_1',
        email: '<EMAIL>',
        name: 'John Student',
        photo: 'https://via.placeholder.com/150',
        givenName: 'John',
        familyName: 'Student',
      },
      {
        id: 'google_2',
        email: '<EMAIL>',
        name: 'Jane Advisor',
        photo: 'https://via.placeholder.com/150',
        givenName: 'Jane',
        familyName: 'Advisor',
      },
      {
        id: 'google_3',
        email: '<EMAIL>',
        name: 'Admin User',
        photo: 'https://via.placeholder.com/150',
        givenName: 'Admin',
        familyName: 'User',
      },
    ];

    // For demo, randomly select a user or use the first one
    return mockGoogleUsers[0];
  }

  async signOut(): Promise<void> {
    try {
      debugLog('Starting sign out process');
      await this.configure();

      // Check if user is signed in with Google
      const isSignedIn = await GoogleSignin.isSignedIn();
      if (isSignedIn) {
        await GoogleSignin.signOut();
        debugLog('Google sign out successful');
      }

      // Clear all stored data
      await storageService.clearAllData();
      debugLog('All user data cleared');

    } catch (error) {
      debugLog('Sign out error:', error);
      // Still clear local data even if Google sign out fails
      await storageService.clearAllData();
    }
  }

  async getCurrentUser(): Promise<GoogleUser | null> {
    try {
      await this.configure();

      const isSignedIn = await GoogleSignin.isSignedIn();
      if (!isSignedIn) return null;

      const userInfo = await GoogleSignin.getCurrentUser();
      if (!userInfo) return null;

      return {
        id: userInfo.user.id,
        email: userInfo.user.email,
        name: userInfo.user.name || '',
        photo: userInfo.user.photo || undefined,
        givenName: userInfo.user.givenName || undefined,
        familyName: userInfo.user.familyName || undefined,
      };
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  async revokeAccess(): Promise<void> {
    try {
      await this.configure();
      await GoogleSignin.revokeAccess();
      await storageService.clearAllData();
      debugLog('Access revoked and data cleared');
    } catch (error) {
      debugLog('Revoke access error:', error);
      await storageService.clearAllData();
    }
  }

  // Restore user session from storage
  async restoreSession(): Promise<GoogleAuthResult | null> {
    try {
      debugLog('Attempting to restore user session');

      const userData = await storageService.getUserData();
      if (!userData) {
        debugLog('No stored user data found');
        return null;
      }

      // Check if tokens are still valid by trying to refresh
      const isValidToken = await apiService.ensureValidToken();
      if (!isValidToken) {
        debugLog('Tokens are invalid, clearing stored data');
        await storageService.clearAllData();
        return null;
      }

      // Get updated tokens
      const tokens = await storageService.getTokens();
      if (!tokens) {
        return null;
      }

      debugLog('Session restored successfully');
      return {
        user: userData.user,
        googleUser: userData.googleUser,
        tokens,
      };
    } catch (error) {
      debugLog('Session restore error:', error);
      await storageService.clearAllData();
      return null;
    }
  }
}

export const googleAuthService = GoogleAuthService.getInstance();
