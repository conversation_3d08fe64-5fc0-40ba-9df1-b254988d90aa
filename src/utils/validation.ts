import * as yup from 'yup';
import { TransactionType } from '../types/Transaction';

export const transactionSchema = (type: TransactionType) => {
  const baseSchema = {
    amount: yup.string().required('Amount is required').test('is-positive', 'Amount must be positive', value => {
      const num = parseFloat(value || '0');
      return !isNaN(num) && num > 0;
    }),
    glCode: yup.string().required('GL Code is required'),
    programCode: yup.string().required('Program Code is required'),
    splitWith: yup.array().of(yup.string()).optional().default([]),
    receiptUri: yup.string().optional(),
    description: yup.string().optional(),
    purpose: yup.string().optional(),
    recipient: yup.string().optional(),
  };

  switch (type) {
    case TransactionType.TRANSFER:
      return yup.object({
        ...baseSchema,
        recipient: yup.string().required('Recipient is required'),
        purpose: yup.string().optional(),
      });

    case TransactionType.SPEND:
      return yup.object({
        ...baseSchema,
        description: yup.string().required('Description is required'),
        // receiptUri: yup.string().required('Receipt is required'),
      });

    case TransactionType.RETURNED:
      return yup.object({
        ...baseSchema,
        description: yup.string().required('Description is required'),
        // receiptUri: yup.string().optional(),
      });

    default:
      return yup.object(baseSchema);
  }
};

// Login schema removed - now using Google OAuth authentication
