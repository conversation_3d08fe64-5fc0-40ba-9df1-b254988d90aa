import React from 'react';
import { User, UserRole } from '../../types/User';
import { apiService } from '../../services/apiService';
import { mockUsers } from '../../store/mockData/users';
import UserPicker from './UserPicker';

interface RecipientPickerProps {
  control: any;
  name: string;
  error?: string;
}

export default function RecipientPicker({
  control,
  name,
  error,
}: RecipientPickerProps) {
  
  // Fetch all users for recipient selection
  const fetchAllUsers = async (): Promise<User[]> => {
    try {
      const fetchedUsers = await apiService.getAllUsers();
      return fetchedUsers;
    } catch (error) {
      console.error('Failed to fetch all users:', error);
      // Fallback to mock data if API fails
      return mockUsers;
    }
  };

  // Filter to exclude current user and show all other users
  const userFilter = (user: User): boolean => {
    // For now, show all users. In a real app, you might want to exclude the current user
    // You could get current user from auth context and filter them out
    return true;
  };

  return (
    <UserPicker
      control={control}
      name={name}
      label="Recipient"
      placeholder="Select recipient"
      error={error}
      multiple={false}
      userFilter={userFilter}
      fetchUsers={fetchAllUsers}
      required={true}
    />
  );
}
