import { API_ENDPOINTS, debugLog } from '../config/env';
import { storageService, AuthTokens } from './storageService';
import { GoogleUser } from './googleAuth';
import { User } from '../types/User';
import { Category } from '../types/Category';
import { TransactionType, TransactionStatus } from '../types/Transaction';

export interface GoogleAuthRequest {
  access_token: string;
  id_token?: string;
}

export interface GoogleAuthResponse {
  access_token: string;
  refresh_token: string;
  user: User;
  expires_in: number;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface RefreshTokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface ApiError {
  message: string;
  status: number;
  details?: any;
}

export interface TransactionCreateRequest {
  submit?: boolean;
  type: TransactionType;
  amount: string;
  currency: string;
  notes?: string;
  category?: string;
  receipt_uri?: string;
  recipient?: string;
  split_with?: string[];
}

export interface TransactionResponse {
  id: string;
  type: TransactionType;
  status: TransactionStatus;
  amount: number;
  currency: string;
  description?: string;
  purpose?: string;
  gl_code: string;
  program_code: string;
  receipt_uri?: string;
  recipient?: string;
  split_with?: string[];
  created_at: string;
  updated_at: string;
  created_by: string;
}

class ApiService {
  private static instance: ApiService;

  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  private async makeRequest<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> {
    try {
      debugLog(`API Request: ${options.method || 'GET'} ${url}`);

      // Ensure Content-Type is always set for requests with body
      const defaultHeaders: Record<string, string> = {};
      if (options.body) {
        defaultHeaders['Content-Type'] = 'application/json';
      }

      const response = await fetch(url, {
        ...options,
        headers: {
          ...defaultHeaders,
          ...options.headers,
        },
      });

      debugLog(`API Response: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const error: ApiError = {
          message: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          status: response.status,
          details: errorData,
        };
        throw error;
      }

      const data = await response.json();
      debugLog('API Response Data:', data);
      return data;
    } catch (error) {
      debugLog('API Error:', error);
      throw error;
    }
  }

  private async makeAuthenticatedRequest<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> {
    const tokens = await storageService.getTokens();

    console.log('makeAuthenticatedRequest Tokens:', tokens);

    if (!tokens) {
      throw new Error('No authentication tokens found');
    }

    // Ensure Content-Type is set for authenticated requests with body
    const authHeaders: Record<string, string> = {
      Authorization: `${tokens.accessToken}`,
    };

    if (options.body) {
      authHeaders['Content-Type'] = 'application/json';
    }

    return this.makeRequest<T>(url, {
      ...options,
      headers: {
        ...authHeaders,
        ...options.headers,
      },
    });
  }

  // Google Authentication
  async authenticateWithGoogle(googleUser: GoogleUser, googleTokens: any): Promise<GoogleAuthResponse> {
    try {
      debugLog('Authenticating with Google backend');
      
      const requestData: GoogleAuthRequest = {
        access_token: googleTokens.accessToken || 'mock-access-token',
        id_token: googleTokens.idToken,
      };

      // For development, simulate the API response
      if (__DEV__) {
        return this.mockGoogleAuth(googleUser);
      }

      const response = await this.makeRequest<GoogleAuthResponse>(
        API_ENDPOINTS.GOOGLE_AUTH,
        {
          method: 'POST',
          body: JSON.stringify(requestData),
        }
      );

      return response;
    } catch (error) {
      debugLog('Google authentication error:', error);
      throw error;
    }
  }

  // Refresh Token
  async refreshAccessToken(refreshToken: string): Promise<RefreshTokenResponse> {
    try {
      debugLog('Refreshing access token');

      // For development, simulate the API response
      if (__DEV__) {
        return this.mockRefreshToken();
      }

      const requestData: RefreshTokenRequest = {
        refresh_token: refreshToken,
      };

      const response = await this.makeRequest<RefreshTokenResponse>(
        API_ENDPOINTS.REFRESH_TOKEN,
        {
          method: 'POST',
          body: JSON.stringify(requestData),
        }
      );

      return response;
    } catch (error) {
      debugLog('Token refresh error:', error);
      throw error;
    }
  }

  // Auto-refresh token if needed
  async ensureValidToken(): Promise<boolean> {
    try {
      const tokens = await storageService.getTokens();
      if (!tokens) {
        return false;
      }

      // Try to refresh the token
      const refreshResponse = await this.refreshAccessToken(tokens.refreshToken);
      
      // Store new tokens
      await storageService.storeTokens({
        accessToken: refreshResponse.access_token,
        refreshToken: refreshResponse.refresh_token,
      });

      return true;
    } catch (error) {
      debugLog('Token refresh failed:', error);
      // Clear invalid tokens
      await storageService.clearTokens();
      return false;
    }
  }

  // Mock responses for development
  private async mockGoogleAuth(googleUser: GoogleUser): Promise<GoogleAuthResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Map Google user to our app user based on email domain
    const user: User = {
      id: googleUser.id,
      email: googleUser.email,
      name: googleUser.name,
      role: googleUser.email.includes('admin') ? 'admin' as any : 
            googleUser.email.includes('advisor') ? 'staff' as any : 'student' as any,
      isAdvisor: googleUser.email.includes('advisor'),
    };

    return {
      access_token: `mock-access-token-${Date.now()}`,
      refresh_token: `mock-refresh-token-${Date.now()}`,
      user,
      expires_in: 3600, // 1 hour
    };
  }

  private async mockRefreshToken(): Promise<RefreshTokenResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      access_token: `mock-access-token-${Date.now()}`,
      refresh_token: `mock-refresh-token-${Date.now()}`,
      expires_in: 3600, // 1 hour
    };
  }

  // Categories API
  async getCategories(): Promise<Category[]> {
    try {
      debugLog('Fetching categories from API');

      const response = await this.makeRequest<Category[]>(
        API_ENDPOINTS.CATEGORIES,
        {
          method: 'GET',
        }
      );

      return response;
    } catch (error) {
      debugLog('Categories fetch error:', error);
      throw error;
    }
  }

  // Students API
  async getStudents(): Promise<User[]> {
    try {
      debugLog('Fetching students from API');

      const response = await this.makeAuthenticatedRequest<User[]>(
        API_ENDPOINTS.STUDENTS,
        {
          method: 'GET',
        }
      );

      return response;
    } catch (error) {
      debugLog('Students fetch error:', error);
      throw error;
    }
  }

  // All Users API (for recipient selection)
  async getAllUsers(): Promise<User[]> {
    try {
      debugLog('Fetching all users from API');

      const response = await this.makeAuthenticatedRequest<User[]>(
        API_ENDPOINTS.USERS,
        {
          method: 'GET',
        }
      );

      return response;
    } catch (error) {
      debugLog('All users fetch error:', error);
      throw error;
    }
  }

  // Transactions API
  async createTransaction(transactionData: TransactionCreateRequest): Promise<TransactionResponse> {
    try {
      debugLog('Creating transaction via API', JSON.stringify(transactionData));

      const response = await this.makeAuthenticatedRequest<TransactionResponse>(
        API_ENDPOINTS.TRANSACTIONS,
        {
          method: 'POST',
          body: JSON.stringify(transactionData),
        }
      );

      return response;
    } catch (error) {
      debugLog('Transaction creation error:', error);
      throw error;
    }
  }
}

export const apiService = ApiService.getInstance();
